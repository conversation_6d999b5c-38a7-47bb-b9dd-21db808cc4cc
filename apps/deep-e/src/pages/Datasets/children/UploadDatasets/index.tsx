import React from 'react';
import { Form, Input, Button, Upload, message, Card } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { uploadDataset } from '@/pages/Datasets/api';
import { ArrowLeftOutlined } from '@ant-design/icons';

interface DatasetFormValues {
  datasetName: string;
  datasetDesc: string;
  file: any;
}

export default function UploadDatasets() {
  const [form] = Form.useForm<DatasetFormValues>();
  const navigate = useNavigate();

  const handleSubmit = async (values: DatasetFormValues) => {
    const formData = new FormData();
    formData.append('datasetName', values.datasetName);
    formData.append('datasetDesc', values.datasetDesc);
    formData.append('file', values.file.file);

    try {
      const res = await uploadDataset(formData);
      if (res.code === 0) {
        message.success(t('数据集上传成功'));
        navigate('/datasets');
      } else {
        message.error(t('数据集上传失败'));
      }
    } catch (error) {
      console.error(t('上传数据集时发生错误:'), error);
      message.error(t('上传数据集时发生错误'));
    }
  };
  const handleBack = () => {
    navigate('/datasets');
  };

  return (
    <div className='h-full p-6'>
      <div className='mb-4'>
        <a
          onClick={handleBack}
          className='flex items-center text-gray-600 hover:text-gray-900 cursor-pointer'>
          <ArrowLeftOutlined className='mr-1' />
          {t('返回列表')}
        </a>
      </div>
      <Card className='w-full'>
        <div className='max-w-[500px]'>
          <Form form={form} onFinish={handleSubmit} layout='vertical'>
            <Form.Item
              label={t('数据集名称')}
              name='datasetName'
              rules={[{ required: true, message: t('请输入数据集名称') }]}>
              <Input />
            </Form.Item>
            <Form.Item
              label={t('数据集描述')}
              name='datasetDesc'
              rules={[{ required: true, message: t('请输入数据集描述') }]}>
              <Input.TextArea rows={4} />
            </Form.Item>
            <Form.Item
              label={t('上传文件')}
              name='file'
              rules={[{ required: true, message: t('请选择要上传的文件') }]}>
              <Upload beforeUpload={() => false}>
                <Button icon={<UploadOutlined />}>{t('点击上传')}</Button>
              </Upload>
            </Form.Item>
            <Form.Item>
              <div className='flex justify-between'>
                <Button type='primary' htmlType='submit'>
                  {t('提交')}
                </Button>
              </div>
            </Form.Item>
          </Form>
        </div>
      </Card>
    </div>
  );
}
