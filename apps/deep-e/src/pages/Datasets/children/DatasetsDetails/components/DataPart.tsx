import React, { useEffect, useState, useMemo } from 'react';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { getDatasetDetail } from '../../../api';
import { DatasetDetailItem } from '../../../api/model';

const calculateContentLength = (value: any): number => {
  if (typeof value === 'string') return value.length;
  if (typeof value === 'number') return String(value).length;
  return JSON.stringify(value).length;
};

type DataPartProps = {
  datasetUuid: string;
  changTotalData: (total: number) => void;
};

const DataPart: React.FC<DataPartProps> = ({ datasetUuid, changTotalData }) => {
  const [dataList, setDataList] = useState<DatasetDetailItem[]>([]);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const fetchDetail = async (page = 1, pageSize = 10) => {
    if (!datasetUuid) return;

    setLoading(true);
    try {
      const res = await getDatasetDetail({ datasetUuid, page, pageSize });
      if (res.code === 0) {
        const list = Array.isArray(res.data) ? res.data : res.data.list;
        setDataList(list || []); // 只保存列表数据，不再设置详情

        if (res.data.page) {
          setPagination({
            current: res.data.page,
            pageSize: res.data.pageSize || 10,
            total: res.data.total || 0,
          });
          changTotalData(res.data.total || 0);
        }
      }
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (newPagination: any) => {
    fetchDetail(newPagination.current, newPagination.pageSize);
  };
  const handleRowClick = (record: DatasetDetailItem) => {
    setExpandedRowKeys((prev) =>
      prev.includes(String(record.id))
        ? prev.filter((key) => key !== String(record.id))
        : [...prev, String(record.id)],
    );
  };
  const columns: ColumnsType<DatasetDetailItem> = useMemo(() => {
    if (!dataList.length) return [];

    // 计算每列内容的最大长度
    const columnLengths: Record<string, number> = {};
    const baseColumns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        key: 'index',
        width: 80,
        align: 'center' as const,
        render: (_: unknown, __: unknown, index: number) => index + 1,
      },
    ];

    if (!dataList[0]?.datasetDetail) return baseColumns;

    // 计算每列内容的最大长度
    Object.keys(dataList[0].datasetDetail).forEach((key) => {
      columnLengths[key] = dataList.reduce((maxLength, item) => {
        const contentLength = calculateContentLength(item.datasetDetail[key]);
        return Math.max(maxLength, contentLength);
      }, key.length);
    });

    // 计算总长度和比例
    const totalLength = Object.values(columnLengths).reduce(
      (sum, len) => sum + len,
      0,
    );
    const tableWidth = window.innerWidth - 200; // 预留边距和滚动条空间

    const dynamicColumns = Object.keys(dataList[0].datasetDetail).map(
      (key) => ({
        title: key,
        dataIndex: ['datasetDetail', key],
        key,
        width: Math.max(
          100,
          Math.floor((columnLengths[key] / totalLength) * tableWidth),
        ),
        render: (value: any, record: DatasetDetailItem) => {
          const isExpanded = expandedRowKeys.includes(record.id.toString());
          if (typeof value === 'string' || typeof value === 'number') {
            return (
              <div
                className={`${
                  isExpanded ? 'max-h-none' : 'max-h-[100px]'
                } overflow-hidden transition-all duration-200`}>
                {value}
              </div>
            );
          }
          return (
            <div
              className={`${
                isExpanded ? 'max-h-none' : 'max-h-[100px]'
              } overflow-hidden transition-all duration-200`}>
              {JSON.stringify(value)}
            </div>
          );
        },
      }),
    );

    return [...baseColumns, ...dynamicColumns];
  }, [dataList, expandedRowKeys]);

  useEffect(() => {
    fetchDetail();
  }, [datasetUuid]);

  return (
    <Table
      bordered
      rowKey='id'
      columns={columns}
      dataSource={dataList}
      loading={loading}
      pagination={pagination}
      onChange={handleTableChange}
      onRow={(record) => ({
        onClick: () => handleRowClick(record),
        style: { cursor: 'pointer' },
      })}
      scroll={{ x: 'max-content' }}
    />
  );
};

export default DataPart;
