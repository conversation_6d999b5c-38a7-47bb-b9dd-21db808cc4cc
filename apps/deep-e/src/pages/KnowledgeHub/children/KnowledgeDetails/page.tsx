import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, message } from 'antd';
import {
  UploadOutlined,
  PlayCircleOutlined,
  ArrowLeftOutlined,
  // SearchOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { getKnowledgeDetailList, analyzeFileToKnowledgeBase } from '../../api';
import type { KnowledgeFile, FileStatus } from '../../types';
import { getToken } from '@/store/features/auth';
import axios from 'axios';
async function downloadFile(fileUuid: string) {
  const token = getToken();
  try {
    const response = await axios.get('/api/file/downloadFileForKnowledge', {
      params: { fileUuid },
      responseType: 'blob',
      headers: { 'X-Token': token, 'Accept-Language': 'zh' },
    });

    // 校验是否为 JSON 错误响应
    if (response.headers['content-type'].includes('application/json')) {
      const errorText = await response.data.text();
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        throw new Error(t('服务器返回错误'));
      }
      throw new Error(errorData.message || t('服务器返回错误'));
    }

    // 提取文件名
    const contentDisposition = response.headers['content-disposition'] || '';
    let filename = 'eval_report.csv';

    const utf8FilenameRegex = /filename\*=UTF-8''([\w%\-\.]+)/i;
    const asciiFilenameRegex = /filename="?([^"]+)"?/i;

    const utf8Matches = contentDisposition.match(utf8FilenameRegex);
    if (utf8Matches && utf8Matches[1]) {
      console.log('utf8FilenameRegex', utf8Matches);
      filename = decodeURIComponent(utf8Matches[1]);
    } else {
      const asciiMatches = contentDisposition.match(asciiFilenameRegex);
      if (asciiMatches && asciiMatches[1]) {
        filename = asciiMatches[1];
      }
    }

    // 校验 data 是否为有效 BlobPart
    if (!(response.data instanceof Blob)) {
      throw new Error(t(`无效的文件数据`));
    }

    // 创建 Blob 并触发下载
    const blob = new Blob([response.data], {
      type: 'application/octet-stream',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error(t(`下载失败:`), error);
    message.error(t(`下载失败`));
  }
}

const KnowledgeDetails: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id, name } = location.state || {};
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showTotal: (total: number) => t('共{total}条', total),
    pageSizeOptions: ['10', '20', '50', '100'],
  });
  // const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<KnowledgeFile[]>([]);

  const handleAnalyze = async (record: KnowledgeFile) => {
    const res = await analyzeFileToKnowledgeBase({
      fileUuid: record.fileUuid,
      baseUuid: record.baseUuid,
    });
    if (!res.code) {
      fetchData({
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
    } else {
      message.error(t('文件解析失败：') + res.msg);
    }
  };

  const fetchData = async (params?: {
    current?: number;
    pageSize?: number;
  }) => {
    setLoading(true);
    try {
      const res = await getKnowledgeDetailList({
        baseUuid: id,
        page: params?.current ?? pagination.current,
        pageSize: params?.pageSize ?? pagination.pageSize,
      });
      setData(res.data.list);
      setPagination((prev) => ({
        ...prev,
        total: res.data.total || 0,
        current: params?.current ?? prev.current,
        pageSize: params?.pageSize ?? prev.pageSize,
      }));
    } catch (e) {
      message.error(t('获取文件列表失败'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current, pagination.pageSize, id]);

  // const handleSearch = (value: string) => {
  //   setSearchText(value);
  //   setPagination((prev) => ({ ...prev, current: 1 }));
  // };

  const StatusTag: React.FC<{ status: FileStatus; record: KnowledgeFile }> = ({
    status,
    record,
  }) => {
    const statusMap: Record<number, { text: string; color: string }> = {
      3: { text: t('未解析'), color: '#999' },
      1: { text: t('解析成功'), color: '#52c41a' },
      2: { text: t('解析失败'), color: '#f5222d' },
      4: { text: t('解析中'), color: '#1890ff' },
    };
    return (
      <Space>
        <span style={{ color: statusMap[record.analyzeState].color }}>
          {status}
        </span>
        {record.analyzeState === 3 && (
          <Button
            type='link'
            icon={<PlayCircleOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleAnalyze(record);
            }}
          />
        )}
      </Space>
    );
  };

  const columns = [
    {
      title: t('文件名称'),
      dataIndex: 'fileName',
      key: 'fileName',
    },
    {
      title: t('文件大小'),
      dataIndex: 'fileSize',
      key: 'fileSize',
    },
    {
      title: t('文件格式'),
      dataIndex: 'fileExt',
      key: 'fileExt',
    },
    {
      title: t('状态'),
      dataIndex: 'analyzeStateName',
      key: 'analyzeStateName',
      render: (status: FileStatus, record: KnowledgeFile) => (
        <StatusTag status={status} record={record} />
      ),
    },
    {
      title: t('导入时间'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time: number) => new Date(time * 1000).toLocaleString(),
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_: any, record: KnowledgeFile) => (
        <Space size='middle'>
          <Button
            type='link'
            onClick={() => {
              downloadFile(record.fileUuid);
            }}>
            {t('下载')}
          </Button>
        </Space>
      ),
    },
  ];

  const extraContent = (
    <Space size='middle'>
      {/* <Input
        placeholder={t('搜索文件')}
        prefix={<SearchOutlined />}
        onChange={(e) => handleSearch(e.target.value)}
        style={{ width: 200 }}
      /> */}
      <Button
        type='primary'
        icon={<UploadOutlined />}
        onClick={() =>
          navigate('/knowledgeHub/upload', { state: { id, name } })
        }>
        {t('导入文件')}
      </Button>
    </Space>
  );

  return (
    <div className='p-6'>
      <div className='mb-4'>
        <Button
          type='link'
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/knowledgeHub')}
          className='px-0'>
          {t('返回')}
        </Button>
      </div>
      <Card
        title={name || t('知识库详情')}
        extra={extraContent}
        className='w-full'>
        <Table
          columns={columns}
          dataSource={data}
          rowKey='fileUuid'
          loading={loading}
          pagination={pagination}
          onChange={(newPagination) => {
            setPagination((prev) => ({
              ...prev,
              current: newPagination.current ?? prev.current,
              pageSize: newPagination.pageSize ?? prev.pageSize,
            }));
          }}
          className='w-full'
        />
      </Card>
    </div>
  );
};

export default KnowledgeDetails;
